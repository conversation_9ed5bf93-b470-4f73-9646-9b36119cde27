import SwiftUI

struct ModelListView: View {
  @EnvironmentObject var viewModel: ModelManagerViewModel
  @EnvironmentObject var cameraViewModel: CameraViewModel
  @State private var showingErrorAlert = false

  var body: some View {
    NavigationView {
      List {
        // MARK: - Models Section
        if !viewModel.availableModels.isEmpty {
          ForEach(viewModel.availableModels) { model in
            ModelRowView(
              model: model,
              isSelected: model == viewModel.appSettings.selectedModel,
              downloadProgress: viewModel.getDownloadProgress(for: model),
              isDownloading: viewModel.isDownloading(model)
            ) {
              selectModel(model)
            } onDelete: {
              viewModel.deleteModel(model)
            } onDownload: {
              viewModel.downloadModel(model)
            } onCancelDownload: {
              viewModel.cancelDownload(for: model.name)
            }
          }
        }
      }
      .navigationTitle("Models")
      .toolbar {
        ToolbarItemGroup(placement: .navigationBarTrailing) {
          Button(action: viewModel.refreshModels) {
            Image(systemName: "arrow.clockwise")
          }
        }
      }
      .refreshable {
        viewModel.refreshModels()
      }
      .alert("Error", isPresented: $showingErrorAlert) {
        Button("OK", role: .cancel) {}
      } message: {
        Text(viewModel.error ?? "An unknown error occurred")
      }
      .onChange(of: viewModel.error) { _, error in
        showingErrorAlert = error != nil
      }
    }
  }

  private func selectModel(_ model: UniversalModelInfo) {
    viewModel.selectModel(model)
    cameraViewModel.loadModel(model)
  }
}

// MARK: - Model Row View
struct ModelRowView: View {
  let model: UniversalModelInfo
  let isSelected: Bool
  let downloadProgress: Double
  let isDownloading: Bool
  let onSelect: () -> Void
  let onDelete: () -> Void
  let onDownload: () -> Void
  let onCancelDownload: () -> Void

  var body: some View {
    HStack {
      VStack(alignment: .leading, spacing: 4) {
        HStack {
          Text(model.name)
            .font(.headline)
            .lineLimit(1)

          Spacer()

          Text(model.modelType.displayName)
            .font(.caption)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(model.modelType == .coreML ? Color.blue.opacity(0.2) : Color.orange.opacity(0.2))
            .foregroundColor(model.modelType == .coreML ? .blue : .orange)
            .cornerRadius(4)
        }

        Text(model.formattedFileSize)
          .font(.caption)
          .foregroundColor(.secondary)
      }

      Spacer()

      Group {
        if isDownloading {
          ZStack {
            CircularProgressView(value: downloadProgress)

            RoundedRectangle(cornerRadius: 2)
              .fill(Color.accentColor)
              .frame(width: 8, height: 8)
          }.highPriorityGesture(
            TapGesture().onEnded {
              onCancelDownload()
            }
          )
        } else if model.modelURL == nil {
          Image(systemName: "arrow.down.circle")
            .resizable()
            .scaledToFit()
            .foregroundColor(.accentColor)
            .highPriorityGesture(
              TapGesture().onEnded {
                onDownload()
              }
            )
        } else if isSelected {
          Image(systemName: "checkmark.circle.fill")
            .resizable()
            .scaledToFit()
            .foregroundColor(.green)
        }
      }
      .frame(width: 24, height: 24)
    }
    .contentShape(Rectangle())
    .onTapGesture {
      if !isDownloading && model.modelURL != nil {
        onSelect()
      }
    }
    .swipeActions(edge: .trailing, allowsFullSwipe: false) {
      if model.modelURL != nil {
        Button("Delete", role: .destructive) {
          onDelete()
        }
      }
    }
  }
}

#Preview {
  ModelListView()
    .environmentObject(ModelManagerViewModel())
    .environmentObject(CameraViewModel())
}
