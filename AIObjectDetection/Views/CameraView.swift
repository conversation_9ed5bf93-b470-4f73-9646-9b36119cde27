import AVFoundation
import <PERSON><PERSON>

struct CameraView: View {
  @EnvironmentObject var viewModel: CameraViewModel
  @State private var showingPermissionAlert = false
  @State private var showingErrorAlert = false

  var body: some View {
    NavigationView {
      ZStack {
        // MARK: - Camera Preview
        GeometryReader { geometry in
          if let previewImage = viewModel.previewImage {
            previewImage
              .resizable()
              .scaledToFill()
              .frame(width: geometry.size.width, height: geometry.size.height)
              .overlay {
                // MARK: - Detection Overlays
                if viewModel.isDetectionEnabled && !viewModel.currentDetections.isEmpty {
                  GeometryReader { geometry in
                    ForEach(viewModel.currentDetections) { detection in
                      DetectionBoxView(
                        detection: detection,
                        containerSize: geometry.size,
                        showConfidence: viewModel.showConfidenceScores
                      )
                    }
                  }
                }
              }
          }
        }
        .ignoresSafeArea()
        .background(.black)

        // MARK: - Top Controls
        VStack {
          if let model = viewModel.appSettings.selectedModel {
            ModelIndicatorView(model: model)
              .padding(.horizontal)
              .padding(.top, 10)
          }

          Spacer()

          // MARK: - Bottom Controls
          BottomControlsView()
            .environmentObject(viewModel)
        }
      }
      .toolbar {
        ToolbarItem(placement: .navigationBarTrailing) {
          Button("Clear") {
            viewModel.clearDetections()
          }
        }
      }
    }
    .onAppear {
      handleCameraPermission()
    }
    .onDisappear {
      viewModel.stopCamera()
    }
    .alert("Camera Permission Required", isPresented: $showingPermissionAlert) {
      Button("Settings") {
        openAppSettings()
      }
      Button("Cancel", role: .cancel) {}
    } message: {
      Text(Constants.ErrorMessages.cameraPermissionDenied)
    }
    .alert("Error", isPresented: $showingErrorAlert) {
      Button("OK", role: .cancel) {}
    } message: {
      Text(viewModel.error ?? "An unknown error occurred")
    }
    .onChange(of: viewModel.cameraPermissionStatus) { _, status in
      handlePermissionChange(status)
    }
    .onChange(of: viewModel.error) { _, error in
      showingErrorAlert = error != nil
    }
  }

  private func handleCameraPermission() {
    switch viewModel.cameraPermissionStatus {
    case .authorized:
      viewModel.startCamera()
    case .notDetermined:
      viewModel.requestCameraPermission()
    case .denied, .restricted:
      showingPermissionAlert = true
    @unknown default:
      break
    }
  }

  private func handlePermissionChange(_ status: AVAuthorizationStatus) {
    switch status {
    case .authorized:
      viewModel.startCamera()
      showingPermissionAlert = false
    case .denied, .restricted:
      showingPermissionAlert = true
    default:
      break
    }
  }

  private func openAppSettings() {
    if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
      UIApplication.shared.open(settingsURL)
    }
  }
}

// MARK: - Detection Box View
struct DetectionBoxView: View {
  let detection: DetectionResult
  let containerSize: CGSize
  let showConfidence: Bool

  // Convert the normalized CGRect to a denormalized CGRect
  private func denormalize(imageViewSize: CGSize, normalizedCGRect: CGRect) -> CGRect {
    let imageViewWidth = imageViewSize.width
    let imageViewHeight = imageViewSize.height

    // Flip the Y coordinate, because the Vision framework uses a coordinate system with the origin in the bottom-left corner, while the SwiftUI uses a coordinate system with the origin in the top-left corner.
    let flippedY = 1.0 - normalizedCGRect.maxY
    return CGRect(
      x: normalizedCGRect.minX * imageViewWidth,
      y: flippedY * imageViewHeight,
      width: normalizedCGRect.width * imageViewWidth,
      height: normalizedCGRect.height * imageViewHeight)
  }

  var body: some View {
    let cgRect = denormalize(imageViewSize: containerSize, normalizedCGRect: detection.boundingBox)
    Rectangle()
      .path(in: cgRect)
      .stroke(
        Color.confidenceColor(for: detection.confidence),
        lineWidth: Constants.detectionBoxLineWidth
      )
      .overlay {
        Text(showConfidence ? detection.displayText : detection.label)
          .font(.system(size: Constants.detectionLabelFontSize, weight: .semibold))
          .foregroundColor(.white)
          .background(Color.confidenceColor(for: detection.confidence))
          .cornerRadius(4)
          .position(x: cgRect.midX, y: cgRect.minY)
      }
  }
}

// MARK: - Supporting Views
struct ModelIndicatorView: View {
  let model: MLModelInfo

  var body: some View {
    Text(model.name)
      .font(.caption)
      .fontWeight(.medium)
      .padding(.horizontal, 12)
      .padding(.vertical, 6)
      .background(.secondary.opacity(0.4))
      .foregroundColor(.white)
      .cornerRadius(20)
  }
}

struct BottomControlsView: View {
  @EnvironmentObject var viewModel: CameraViewModel

  var body: some View {
    VStack(spacing: 16) {
      // Detection Toggle
      HStack {
        Text("Detection")
          .foregroundColor(.white)
          .fontWeight(.medium)

        Spacer()

        Toggle(
          "",
          isOn: Binding(
            get: { viewModel.isDetectionEnabled },
            set: { viewModel.isDetectionEnabled = $0 }
          )
        )
        .toggleStyle(SwitchToggleStyle())
      }
      .padding(.horizontal, 20)
      .padding(.vertical, 12)
      .background(.secondary.opacity(0.4))
      .cornerRadius(12)

      // Confidence Threshold Slider
      if viewModel.isDetectionEnabled {
        VStack {
          HStack {
            Text("Confidence")
              .foregroundColor(.white)
              .fontWeight(.medium)

            Spacer()

            Text("\(Int(viewModel.confidenceThreshold * 100))%")
              .foregroundColor(.white)
              .fontWeight(.medium)
          }

          Slider(
            value: Binding(
              get: { viewModel.confidenceThreshold },
              set: { viewModel.confidenceThreshold = $0 }
            ),
            in: 0.1...1.0,
            step: 0.05
          )
          .accentColor(.white)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(.secondary.opacity(0.4))
        .cornerRadius(12)
      }
    }
    .padding(.horizontal)
    .padding(.bottom, 30)
  }
}

#Preview {
  CameraView()
    .environmentObject(CameraViewModel())
}
