import Foundation
import CoreImage

/// Protocol defining the interface for object detection models
protocol ObjectDetectionModel: AnyObject {
  
  // MARK: - Model Properties
  var modelInfo: any ModelInfo { get }
  var isLoaded: Bool { get }
  var modelType: ModelType { get }
  
  // MARK: - Model Lifecycle
  /// Loads the model from the specified URL
  func loadModel() async throws
  
  /// Unloads the model and frees memory
  func unloadModel()
  
  // MARK: - Prediction
  /// Performs object detection on the provided image
  /// - Parameter ciImage: The input image for detection
  /// - Returns: Array of detection results
  func predict(ciImage: CIImage) async throws -> [DetectionResult]
  
  // MARK: - Configuration
  /// Sets the confidence threshold for filtering detections
  func setConfidenceThreshold(_ threshold: Float)
  
  /// Gets the current confidence threshold
  func getConfidenceThreshold() -> Float
}

/// Errors that can occur during model operations
enum ModelError: LocalizedError {
  case modelNotFound
  case modelLoadingFailed(String)
  case predictionFailed(String)
  case invalidModelFormat
  case unsupportedModelType
  case modelNotLoaded
  
  var errorDescription: String? {
    switch self {
    case .modelNotFound:
      return "Model file not found."
    case .modelLoadingFailed(let message):
      return "Failed to load model: \(message)"
    case .predictionFailed(let message):
      return "Prediction failed: \(message)"
    case .invalidModelFormat:
      return "The model format is not supported."
    case .unsupportedModelType:
      return "The model type is not supported."
    case .modelNotLoaded:
      return "Model must be loaded before making predictions."
    }
  }
}

/// Factory for creating model handlers based on model type
class ModelFactory {
  
  /// Creates an appropriate model handler for the given model info
  /// - Parameter modelInfo: Information about the model to create
  /// - Returns: A model handler conforming to ObjectDetectionModel
  static func createModel(for modelInfo: any ModelInfo) -> ObjectDetectionModel {
    switch modelInfo.modelType {
    case .coreML:
      return CoreMLModelHandler(modelInfo: modelInfo)
    case .onnx:
      return ONNXModelHandler(modelInfo: modelInfo)
    }
  }
}

/// Base class providing common functionality for model handlers
open class BaseModelHandler: ObjectDetectionModel {
  
  // MARK: - Properties
  let modelInfo: any ModelInfo
  private(set) var isLoaded: Bool = false
  private var confidenceThreshold: Float = 0.5
  
  var modelType: ModelType {
    return modelInfo.modelType
  }
  
  // MARK: - Initialization
  init(modelInfo: any ModelInfo) {
    self.modelInfo = modelInfo
  }
  
  // MARK: - Abstract Methods (to be overridden)
  open func loadModel() async throws {
    fatalError("loadModel() must be implemented by subclasses")
  }

  open func unloadModel() {
    isLoaded = false
  }

  open func predict(ciImage: CIImage) async throws -> [DetectionResult] {
    fatalError("predict(ciImage:) must be implemented by subclasses")
  }
  
  // MARK: - Configuration
  func setConfidenceThreshold(_ threshold: Float) {
    confidenceThreshold = threshold
  }
  
  func getConfidenceThreshold() -> Float {
    return confidenceThreshold
  }
  
  // MARK: - Protected Methods
  func setLoaded(_ loaded: Bool) {
    isLoaded = loaded
  }

  func validateModelLoaded() throws {
    guard isLoaded else {
      throw ModelError.modelNotLoaded
    }
  }
}
