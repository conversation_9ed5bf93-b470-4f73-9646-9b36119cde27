import Foundation

/// Represents information about a CoreML model
struct MLModelInfo: Identifiable, Codable, Equatable {
  let name: String
  let downloadURL: String
  let fileSize: Int64

  var id: String { name }

  init(
    name: String,
    downloadURL: String,
    fileSize: Int64 = 0,
  ) {
    self.name = name
    self.downloadURL = downloadURL
    self.fileSize = fileSize
  }

  /// Returns formatted file size string
  var formattedFileSize: String {
    return fileSize.formatted(
      .byteCount(style: .file, allowedUnits: [.kb, .mb, .gb], spellsOutZero: true))
  }

  /// Returns the model file URL if available
  var modelURL: URL? {
    guard
      let contents = try? FileManager.default.contentsOfDirectory(
        at: FileManager.default.modelsDirectory,
        includingPropertiesForKeys: nil,
      )
    else { return nil }

    let paths = contents.filter { fileURL in
      fileURL.pathExtension == Constants.compiledModelExtension
    }
    let filePath = paths.first { $0.lastPathComponent.withoutExtension == name }?.path

    guard let filePath = filePath else { return nil }
    return URL(fileURLWithPath: filePath)
  }

  static func == (lhs: MLModelInfo, rhs: MLModelInfo) -> Bool {
    return lhs.id == rhs.id
  }
}

extension MLModelInfo {
  static var mock: [MLModelInfo] {
    return (1...100).map {
      MLModelInfo(
        name: "Model \($0)",
        downloadURL: "",
        fileSize: 1024 * Int64.random(in: 10_000..<1_000_000),
      )
    }
  }
}
