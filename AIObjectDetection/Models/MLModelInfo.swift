import Foundation

/// Supported model types for object detection
enum ModelType: String, CaseIterable, Codable {
  case coreML = "mlmodelc"
  case onnx = "onnx"

  var fileExtension: String {
    return self.rawValue
  }

  var displayName: String {
    switch self {
    case .coreML:
      return "CoreML"
    case .onnx:
      return "ONNX"
    }
  }
}

/// Protocol for model information abstraction
protocol ModelInfo: Identifiable, Codable, Equatable {
  var name: String { get }
  var downloadURL: String { get }
  var fileSize: Int64 { get }
  var modelType: ModelType { get }
  var formattedFileSize: String { get }
  var modelURL: URL? { get }
}

/// Represents information about any supported model type
struct UniversalModelInfo: ModelInfo {
  let name: String
  let downloadURL: String
  let fileSize: Int64
  let modelType: ModelType

  var id: String { "\(modelType.rawValue)_\(name)" }

  init(
    name: String,
    downloadURL: String,
    fileSize: Int64 = 0,
    modelType: ModelType
  ) {
    self.name = name
    self.downloadURL = downloadURL
    self.fileSize = fileSize
    self.modelType = modelType
  }

  /// Returns formatted file size string
  var formattedFileSize: String {
    return fileSize.formatted(
      .byteCount(style: .file, allowedUnits: [.kb, .mb, .gb], spellsOutZero: true))
  }

  /// Returns the model file URL if available
  var modelURL: URL? {
    guard
      let contents = try? FileManager.default.contentsOfDirectory(
        at: FileManager.default.modelsDirectory,
        includingPropertiesForKeys: nil,
      )
    else { return nil }

    let paths = contents.filter { fileURL in
      fileURL.pathExtension == modelType.fileExtension
    }
    let filePath = paths.first { $0.lastPathComponent.withoutExtension == name }?.path

    guard let filePath = filePath else { return nil }
    return URL(fileURLWithPath: filePath)
  }

  static func == (lhs: UniversalModelInfo, rhs: UniversalModelInfo) -> Bool {
    return lhs.id == rhs.id
  }
}

/// Legacy CoreML model info for backward compatibility
typealias MLModelInfo = UniversalModelInfo

extension UniversalModelInfo {
  static var mock: [UniversalModelInfo] {
    return (1...50).map {
      UniversalModelInfo(
        name: "CoreML Model \($0)",
        downloadURL: "",
        fileSize: 1024 * Int64.random(in: 10_000..<1_000_000),
        modelType: .coreML
      )
    } + (51...100).map {
      UniversalModelInfo(
        name: "ONNX Model \($0)",
        downloadURL: "",
        fileSize: 1024 * Int64.random(in: 10_000..<1_000_000),
        modelType: .onnx
      )
    }
  }
}
