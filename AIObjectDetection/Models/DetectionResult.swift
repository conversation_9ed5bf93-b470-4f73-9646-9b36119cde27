import Vision

/// Represents a single object detection result
struct DetectionResult: Identifiable, Equatable {
  let id = UUID()
  let boundingBox: CGRect
  let confidence: Float
  let label: String
  let timestamp: Date

  init(boundingBox: CGRect, confidence: Float, label: String) {
    self.boundingBox = boundingBox
    self.confidence = confidence
    self.label = label
    self.timestamp = Date()
  }

  /// Creates a DetectionResult from a VNRecognizedObjectObservation
  init(from observation: VNRecognizedObjectObservation) {
    self.boundingBox = observation.boundingBox
    self.confidence = observation.confidence
    self.label = observation.labels.first?.identifier ?? "Unknown"
    self.timestamp = Date()
  }

  /// Returns a formatted confidence percentage string
  var confidencePercentage: String {
    return confidence.formatted(.percent.precision(.fractionLength(1)))
  }

  /// Returns a display string combining label and confidence
  var displayText: String {
    return "\(label) (\(confidencePercentage))"
  }

  static func == (lhs: DetectionResult, rhs: DetectionResult) -> Bool {
    return lhs.id == rhs.id
  }
}
