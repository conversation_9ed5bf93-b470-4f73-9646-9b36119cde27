import Foundation
import CoreImage
import Combine

/// Unified detection manager that works with any ObjectDetectionModel
class DetectionManager: ObservableObject {
  
  // MARK: - Settings Manager
  private let appSettings = AppSettings.shared
  
  // MARK: - Published Properties
  @Published var currentDetections: [DetectionResult] = []
  @Published var error: ModelError?
  @Published var isModelLoaded: Bool = false
  
  // MARK: - Private Properties
  private var currentModel: ObjectDetectionModel?
  private var frameCounter = 0
  private let processingQueue = DispatchQueue(
    label: "detection.processing.queue", qos: .userInitiated)
  private var lastProcessingTime = Date()
  private var cancellables = Set<AnyCancellable>()
  
  // MARK: - Initialization
  static let shared = DetectionManager()
  
  private init() {
    setupSettingsObservation()
  }
  
  // MARK: - Public Methods
  
  /// Loads a model for detection
  func loadModel(_ modelInfo: any ModelInfo) async {
    await MainActor.run { 
      error = nil
      isModelLoaded = false
    }
    
    do {
      // Unload current model if any
      currentModel?.unloadModel()
      
      // Create new model handler
      let model = ModelFactory.createModel(for: modelInfo)
      
      // Load the model
      try await model.loadModel()
      
      // Update confidence threshold
      model.setConfidenceThreshold(Float(appSettings.confidenceThreshold))
      
      // Store the model
      currentModel = model
      
      await MainActor.run { 
        isModelLoaded = true
      }
      
      print("Successfully loaded model: \(modelInfo.name) (\(modelInfo.modelType.displayName))")
      
    } catch let modelError as ModelError {
      await MainActor.run { 
        self.error = modelError
        isModelLoaded = false
      }
      print("Failed to load model: \(modelError.localizedDescription)")
      
    } catch {
      await MainActor.run { 
        self.error = .modelLoadingFailed(error.localizedDescription)
        isModelLoaded = false
      }
      print("Failed to load model: \(error)")
    }
  }
  
  /// Processes a video frame for object detection
  func processFrame(_ ciImage: CIImage) {
    guard appSettings.isDetectionEnabled,
          let model = currentModel,
          model.isLoaded,
          shouldProcessFrame()
    else {
      return
    }
    
    processingQueue.async { [weak self] in
      self?.performDetection(on: ciImage, using: model)
    }
  }
  
  /// Clears current detections
  func clearDetections() {
    Task { @MainActor in
      currentDetections = []
    }
  }
  
  /// Unloads the current model
  func unloadModel() {
    currentModel?.unloadModel()
    currentModel = nil
    Task { @MainActor in
      isModelLoaded = false
      currentDetections = []
    }
  }
  
  /// Updates the confidence threshold for the current model
  func updateConfidenceThreshold(_ threshold: Double) {
    currentModel?.setConfidenceThreshold(Float(threshold))
  }
  
  // MARK: - Private Methods
  
  private func setupSettingsObservation() {
    // Observe confidence threshold changes
    appSettings.$confidenceThreshold
      .sink { [weak self] newThreshold in
        self?.updateConfidenceThreshold(newThreshold)
      }
      .store(in: &cancellables)
    
    // Clear detections when detection is disabled
    appSettings.$isDetectionEnabled
      .sink { [weak self] isEnabled in
        if !isEnabled {
          self?.clearDetections()
        }
      }
      .store(in: &cancellables)
  }
  
  private func shouldProcessFrame() -> Bool {
    frameCounter += 1
    
    // Check frame interval
    if frameCounter % appSettings.frameProcessingInterval != 0 {
      return false
    }
    
    // Check time-based throttling (minimum 100ms between processing)
    let now = Date()
    if now.timeIntervalSince(lastProcessingTime) < 0.1 {
      return false
    }
    
    lastProcessingTime = now
    return true
  }
  
  private func performDetection(on ciImage: CIImage, using model: ObjectDetectionModel) {
    Task {
      do {
        let detections = try await model.predict(ciImage: ciImage)
        
        await MainActor.run {
          self.currentDetections = detections
          self.error = nil
        }
        
      } catch let modelError as ModelError {
        await MainActor.run {
          self.error = modelError
        }
        
      } catch {
        await MainActor.run {
          self.error = .predictionFailed(error.localizedDescription)
        }
      }
    }
  }
}

// MARK: - Model Information
extension DetectionManager {
  
  /// Gets information about the currently loaded model
  var currentModelInfo: (any ModelInfo)? {
    return currentModel?.modelInfo
  }
  
  /// Gets the type of the currently loaded model
  var currentModelType: ModelType? {
    return currentModel?.modelType
  }
  
  /// Checks if a model is currently loaded
  var hasLoadedModel: Bool {
    return currentModel?.isLoaded ?? false
  }
}
