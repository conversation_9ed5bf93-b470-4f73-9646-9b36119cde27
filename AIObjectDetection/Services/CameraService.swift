import AVFoundation
import CoreImage
import UIKit

/// Manages camera capture session and video output
class CameraService: NSObject, ObservableObject {

  // MARK: - Published Properties
  @Published var cameraPermissionStatus: AVAuthorizationStatus = .notDetermined
  @Published var error: CameraError?

  // MARK: - Private Properties
  private let captureSession = AVCaptureSession()
  private let videoOutput: AVCaptureVideoDataOutput = AVCaptureVideoDataOutput()
  private let captureDevice: AVCaptureDevice? = AVCaptureDevice.default(
    .builtInWideAngleCamera, for: .video, position: .back)
  private var isCaptureSessionConfigured = false
  private let sessionQueue = DispatchQueue(label: "camera.session.queue")
  private let videoOutputQueue = DispatchQueue(label: "camera.video.output.queue")

  // MARK: - Delegates
  weak var frameDelegate: CameraFrameDelegate?

  // MARK: - Initialization
  static let shared = CameraService()
  override private init() {
    super.init()
    checkCameraPermission()
  }

  // MARK: - Public Methods

  /// Starts the camera capture session
  func startSession() {
    print("Starting camera")
    if isCaptureSessionConfigured {
      if !captureSession.isRunning {
        sessionQueue.async { [weak self] in
          self?.captureSession.startRunning()
        }
      }
    } else {
      // Configure the capture session
      sessionQueue.async { [weak self] in
        guard let self = self else { return }
        self.captureSession.beginConfiguration()

        defer {
          self.captureSession.commitConfiguration()
          if self.isCaptureSessionConfigured {
            self.captureSession.startRunning()
          }
        }

        self.captureSession.inputs.forEach { self.captureSession.removeInput($0) }
        self.captureSession.outputs.forEach { self.captureSession.removeOutput($0) }

        guard let captureDevice = self.captureDevice else {
          Task { @MainActor in self.error = .cameraNotAvailable }
          return
        }

        guard let deviceInput = try? AVCaptureDeviceInput(device: captureDevice) else {
          print("Failed to get device input")
          Task { @MainActor in self.error = .configurationFailed }
          return
        }

        self.videoOutput.setSampleBufferDelegate(self, queue: videoOutputQueue)
        self.videoOutput.alwaysDiscardsLateVideoFrames = true

        // Configure video settings
        self.videoOutput.videoSettings = [
          kCVPixelBufferPixelFormatTypeKey as String: kCVPixelFormatType_32BGRA
        ]

        guard self.captureSession.canAddInput(deviceInput) else {
          print("Unable to add device input to capture session.")
          Task { @MainActor in self.error = .configurationFailed }
          return
        }

        guard self.captureSession.canAddOutput(videoOutput) else {
          print("Unable to add video output to capture session.")
          Task { @MainActor in self.error = .configurationFailed }
          return
        }

        self.captureSession.addInput(deviceInput)
        self.captureSession.addOutput(videoOutput)
        self.captureSession.sessionPreset = .high

        for connection in videoOutput.connections {
          let portraitAngle: CGFloat = 90  // 90 degrees for portrait orientation
          if connection.isVideoRotationAngleSupported(portraitAngle) {
            connection.videoRotationAngle = portraitAngle
          }
          if connection.isVideoMirroringSupported {
            connection.isVideoMirrored = false
          }
        }

        self.isCaptureSessionConfigured = true
        print("Camera is configured")
      }
    }
  }

  /// Stops the camera capture session
  func stopSession() {
    print("Stopping camera")
    guard isCaptureSessionConfigured else {
      print("Capture session is not configured.")
      return
    }
    if captureSession.isRunning {
      sessionQueue.async { [weak self] in
        self?.captureSession.stopRunning()
      }
    }
  }

  /// Requests camera permission
  func requestCameraPermission() {
    Task {
      let granted = await AVCaptureDevice.requestAccess(for: .video)
      await MainActor.run {
        self.cameraPermissionStatus = granted ? .authorized : .denied
        if !granted { self.error = .permissionDenied }
      }
      if granted { startSession() }
    }
  }

  /// Returns the capture session for preview layer
  func getCaptureSession() -> AVCaptureSession {
    return captureSession
  }

  // MARK: - Private Methods

  private func checkCameraPermission() {
    cameraPermissionStatus = AVCaptureDevice.authorizationStatus(for: .video)
  }

  private func getOrientation() -> CGImagePropertyOrientation {
    switch UIDevice.current.orientation {
    case .portraitUpsideDown:  // Device oriented vertically, home button on the top
      return .down
    case .landscapeLeft:  // Device oriented horizontally, home button on the right
      return .left
    case .landscapeRight:  // Device oriented horizontally, home button on the left
      return .right
    case .portrait:  // Device oriented vertically, home button on the bottom
      return .up
    default:
      return .up
    }
  }
}

// MARK: - AVCaptureVideoDataOutputSampleBufferDelegate
extension CameraService: AVCaptureVideoDataOutputSampleBufferDelegate {

  func captureOutput(
    _ output: AVCaptureOutput,
    didOutput sampleBuffer: CMSampleBuffer,
    from connection: AVCaptureConnection,
  ) {
    // Forward frame to delegate for processing
    guard let imageBuffer = sampleBuffer.imageBuffer else { return }
    let ciImage = CIImage(cvImageBuffer: imageBuffer).oriented(getOrientation())
    frameDelegate?.didReceiveImage(ciImage)
  }

  func captureOutput(
    _ output: AVCaptureOutput,
    didDrop sampleBuffer: CMSampleBuffer,
    from connection: AVCaptureConnection,
  ) {
    // Handle dropped frames if needed
    print("A video frame was discarded.")
  }
}

// MARK: - Supporting Types

/// Delegate protocol for receiving camera frames
protocol CameraFrameDelegate: AnyObject {
  func didReceiveImage(_ ciImage: CIImage)
}

/// Camera-related errors
enum CameraError: LocalizedError {
  case permissionDenied
  case cameraNotAvailable
  case configurationFailed

  var errorDescription: String? {
    switch self {
    case .permissionDenied:
      return Constants.ErrorMessages.cameraPermissionDenied
    case .cameraNotAvailable:
      return Constants.ErrorMessages.cameraNotAvailable
    case .configurationFailed:
      return "Failed to configure camera session."
    }
  }
}
