import SwiftUI

/// Centralized app settings manager using @AppStorage
/// Provides a single source of truth for all app preferences
class AppSettings: ObservableObject {

  // MARK: - Detection Settings
  @AppStorage(Constants.AppStorageKeys.isDetectionEnabled)
  var isDetectionEnabled: Bool = Constants.DefaultSettings.isDetectionEnabled {
    didSet {
      objectWillChange.send()
    }
  }

  @AppStorage(Constants.AppStorageKeys.confidenceThreshold)
  var confidenceThreshold: Double = Constants.DefaultSettings.confidenceThreshold {
    didSet {
      objectWillChange.send()
    }
  }

  @AppStorage(Constants.AppStorageKeys.showConfidenceScores)
  var showConfidenceScores: Bool = Constants.DefaultSettings.showConfidenceScores {
    didSet {
      objectWillChange.send()
    }
  }

  @AppStorage(Constants.AppStorageKeys.frameProcessingInterval)
  var frameProcessingInterval: Int = Constants.DefaultSettings.frameProcessingInterval {
    didSet {
      objectWillChange.send()
    }
  }

  // MARK: - Model Settings
  private var _selectedModel: UniversalModelInfo? {
    get {
      guard
        let json = UserDefaults.standard.string(forKey: Constants.AppStorageKeys.selectedModel),
        let data = json.data(using: .utf8)
      else {
        return nil
      }
      return try? decoder.decode(UniversalModelInfo.self, from: data)
    }
    set {
      if let newValue = newValue {
        let data = try! encoder.encode(newValue)
        let json = String(data: data, encoding: .utf8)
        UserDefaults.standard.set(json, forKey: Constants.AppStorageKeys.selectedModel)
      } else {
        UserDefaults.standard.removeObject(forKey: Constants.AppStorageKeys.selectedModel)
      }
      objectWillChange.send()
    }
  }

  var selectedModel: UniversalModelInfo? {
    get { _selectedModel }
    set { _selectedModel = newValue }
  }

  // MARK: - Private Properties
  private let encoder = JSONEncoder()
  private let decoder = JSONDecoder()

  // MARK: - Initialization
  static let shared = AppSettings()
  private init() {}

  // MARK: - Public Methods

  /// Resets all settings to their default values
  func resetToDefaults() {
    isDetectionEnabled = Constants.DefaultSettings.isDetectionEnabled
    confidenceThreshold = Constants.DefaultSettings.confidenceThreshold
    showConfidenceScores = Constants.DefaultSettings.showConfidenceScores
    frameProcessingInterval = Constants.DefaultSettings.frameProcessingInterval
    selectedModel = nil
  }

  // MARK: - Computed Properties for UI

  /// Returns confidence threshold as percentage string
  var confidenceThresholdPercentage: String {
    return confidenceThreshold.formatted(.percent)
  }

  /// Returns frame processing interval description
  var frameProcessingDescription: String {
    switch frameProcessingInterval {
    case 1:
      return "Every frame (High CPU usage)"
    case 2...5:
      return "Every \(frameProcessingInterval) frames (Balanced)"
    case 6...15:
      return "Every \(frameProcessingInterval) frames (Low CPU usage)"
    default:
      return "Every \(frameProcessingInterval) frames"
    }
  }
}

// MARK: - Settings Sections for UI
extension AppSettings {

  /// Detection settings section data for UI
  var detectionSettings: [(String, String, Any)] {
    return [
      ("Detection Enabled", "Enable/disable object detection", isDetectionEnabled),
      ("Confidence Threshold", confidenceThresholdPercentage, confidenceThreshold),
      ("Show Confidence Scores", "Display confidence percentages", showConfidenceScores),
      ("Processing Interval", frameProcessingDescription, frameProcessingInterval),
    ]
  }
}
