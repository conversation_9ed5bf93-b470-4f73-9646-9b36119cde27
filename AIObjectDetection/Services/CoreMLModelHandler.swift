import Foundation
import CoreML
import Vision
import CoreImage

/// CoreML implementation of ObjectDetectionModel
class CoreMLModelHandler: BaseModelHandler {
  
  // MARK: - Private Properties
  private var visionModel: VNCoreMLModel?
  private var mlModel: MLModel?
  
  // MARK: - Model Lifecycle
  override func loadModel() async throws {
    guard let modelURL = modelInfo.modelURL else {
      throw ModelError.modelNotFound
    }
    
    do {
      // Validate file extension
      guard modelURL.pathExtension == ModelType.coreML.fileExtension else {
        throw ModelError.invalidModelFormat
      }
      
      // Configure MLModel
      let config = MLModelConfiguration()
      config.computeUnits = .all
      
      // Load MLModel
      let mlModel = try MLModel(contentsOf: modelURL, configuration: config)
      
      // Create VNCoreMLModel for Vision framework
      let visionModel = try VNCoreMLModel(for: mlModel)
      
      // Store models
      self.mlModel = mlModel
      self.visionModel = visionModel
      setLoaded(true)
      
      print("Successfully loaded CoreML model: \(modelInfo.name)")
      
    } catch {
      setLoaded(false)
      throw ModelError.modelLoadingFailed(error.localizedDescription)
    }
  }
  
  override func unloadModel() {
    visionModel = nil
    mlModel = nil
    super.unloadModel()
    print("Unloaded CoreML model: \(modelInfo.name)")
  }
  
  // MARK: - Prediction
  override func predict(ciImage: CIImage) async throws -> [DetectionResult] {
    try validateModelLoaded()
    
    guard let visionModel = visionModel else {
      throw ModelError.modelNotLoaded
    }
    
    return try await withCheckedThrowingContinuation { continuation in
      let request = VNCoreMLRequest(model: visionModel) { [weak self] request, error in
        if let error = error {
          continuation.resume(throwing: ModelError.predictionFailed(error.localizedDescription))
          return
        }
        
        guard let self = self else {
          continuation.resume(throwing: ModelError.predictionFailed("Model handler deallocated"))
          return
        }
        
        do {
          let results = try self.processVisionResults(request.results)
          continuation.resume(returning: results)
        } catch {
          continuation.resume(throwing: error)
        }
      }
      
      // Configure request
      request.imageCropAndScaleOption = .scaleFill
      
      let handler = VNImageRequestHandler(ciImage: ciImage)
      
      do {
        try handler.perform([request])
      } catch {
        continuation.resume(throwing: ModelError.predictionFailed(error.localizedDescription))
      }
    }
  }
  
  // MARK: - Private Methods
  private func processVisionResults(_ results: [Any]?) throws -> [DetectionResult] {
    guard let observations = results as? [VNRecognizedObjectObservation] else {
      return []
    }
    
    // Filter observations by confidence threshold
    let filteredObservations = observations.filter { observation in
      observation.confidence >= getConfidenceThreshold()
    }
    
    // Convert to DetectionResult objects
    return filteredObservations.map { observation in
      DetectionResult(from: observation)
    }
  }
}

// MARK: - CoreML Specific Extensions
extension CoreMLModelHandler {
  
  /// Gets the CoreML model description if available
  var modelDescription: MLModelDescription? {
    return mlModel?.modelDescription
  }
  
  /// Gets the CoreML model metadata
  var modelMetadata: [String: Any]? {
    return mlModel?.modelDescription.metadata as? [String: Any]
  }
  
  /// Gets input feature descriptions
  var inputDescriptions: [MLFeatureDescription] {
    return mlModel?.modelDescription.inputDescriptionsByName.values.map { $0 } ?? []
  }
  
  /// Gets output feature descriptions
  var outputDescriptions: [MLFeatureDescription] {
    return mlModel?.modelDescription.outputDescriptionsByName.values.map { $0 } ?? []
  }
}
