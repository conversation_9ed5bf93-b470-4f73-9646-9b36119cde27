import Foundation
import CoreImage
import OnnxRuntimeBindings

/// ONNX implementation of ObjectDetectionModel
class ONNXModelHandler: BaseModelHandler {
  
  // MARK: - Private Properties
  private var ortSession: ORTSession?
  private var ortEnv: ORTEnv?
  
  // MARK: - Model Lifecycle
  override func loadModel() async throws {
    guard let modelURL = modelInfo.modelURL else {
      throw ModelError.modelNotFound
    }
    
    do {
      // Validate file extension
      guard modelURL.pathExtension == ModelType.onnx.fileExtension else {
        throw ModelError.invalidModelFormat
      }
      
      // Create ONNX Runtime environment
      let env = try ORTEnv(loggingLevel: .warning)
      
      // Create session options
      let sessionOptions = try ORTSessionOptions()
      
      // Configure for optimal performance on iOS
      try sessionOptions.setIntraOpNumThreads(2)
      try sessionOptions.setInterOpNumThreads(2)
      
      // Create session
      let session = try ORTSession(env: env, modelPath: modelURL.path, sessionOptions: sessionOptions)
      
      // Store references
      self.ortEnv = env
      self.ortSession = session
      setLoaded(true)
      
      print("Successfully loaded ONNX model: \(modelInfo.name)")
      
    } catch {
      setLoaded(false)
      throw ModelError.modelLoadingFailed(error.localizedDescription)
    }
  }
  
  override func unloadModel() {
    ortSession = nil
    ortEnv = nil
    super.unloadModel()
    print("Unloaded ONNX model: \(modelInfo.name)")
  }
  
  // MARK: - Prediction
  override func predict(ciImage: CIImage) async throws -> [DetectionResult] {
    try validateModelLoaded()
    
    guard let session = ortSession else {
      throw ModelError.modelNotLoaded
    }
    
    do {
      // Preprocess image
      let inputTensor = try preprocessImage(ciImage)
      
      // Run inference
      let outputs = try session.run(withInputs: ["input": inputTensor], outputNames: nil, runOptions: nil)
      
      // Postprocess results
      let detections = try postprocessOutputs(outputs)
      
      return detections
      
    } catch {
      throw ModelError.predictionFailed(error.localizedDescription)
    }
  }
  
  // MARK: - Private Methods
  
  /// Preprocesses the input image for ONNX model
  private func preprocessImage(_ ciImage: CIImage) throws -> ORTValue {
    // Convert CIImage to pixel buffer
    let context = CIContext()
    let inputSize = CGSize(width: 640, height: 640) // Standard YOLO input size
    
    // Resize image
    let resizedImage = ciImage.transformed(by: CGAffineTransform(scaleX: inputSize.width / ciImage.extent.width, 
                                                                y: inputSize.height / ciImage.extent.height))
    
    // Create pixel buffer
    var pixelBuffer: CVPixelBuffer?
    let status = CVPixelBufferCreate(kCFAllocatorDefault, 
                                   Int(inputSize.width), 
                                   Int(inputSize.height), 
                                   kCVPixelFormatType_32BGRA, 
                                   nil, 
                                   &pixelBuffer)
    
    guard status == kCVReturnSuccess, let buffer = pixelBuffer else {
      throw ModelError.predictionFailed("Failed to create pixel buffer")
    }
    
    // Render image to pixel buffer
    context.render(resizedImage, to: buffer)
    
    // Convert to tensor format (NCHW: batch, channels, height, width)
    let tensorData = try convertPixelBufferToTensorData(buffer)
    
    // Create ONNX tensor
    let shape: [NSNumber] = [1, 3, NSNumber(value: inputSize.height), NSNumber(value: inputSize.width)]
    return try ORTValue(tensorData: tensorData, elementType: .float, shape: shape)
  }
  
  /// Converts pixel buffer to tensor data
  private func convertPixelBufferToTensorData(_ pixelBuffer: CVPixelBuffer) throws -> NSMutableData {
    CVPixelBufferLockBaseAddress(pixelBuffer, .readOnly)
    defer { CVPixelBufferUnlockBaseAddress(pixelBuffer, .readOnly) }
    
    let width = CVPixelBufferGetWidth(pixelBuffer)
    let height = CVPixelBufferGetHeight(pixelBuffer)
    let bytesPerRow = CVPixelBufferGetBytesPerRow(pixelBuffer)
    
    guard let baseAddress = CVPixelBufferGetBaseAddress(pixelBuffer) else {
      throw ModelError.predictionFailed("Failed to get pixel buffer base address")
    }
    
    let tensorData = NSMutableData(capacity: width * height * 3 * MemoryLayout<Float>.size)!
    let floatPtr = tensorData.mutableBytes.assumingMemoryBound(to: Float.self)
    
    // Convert BGRA to RGB and normalize to [0, 1]
    for y in 0..<height {
      let rowPtr = baseAddress.advanced(by: y * bytesPerRow).assumingMemoryBound(to: UInt8.self)
      for x in 0..<width {
        let pixelPtr = rowPtr.advanced(by: x * 4)
        let b = Float(pixelPtr[0]) / 255.0
        let g = Float(pixelPtr[1]) / 255.0
        let r = Float(pixelPtr[2]) / 255.0
        
        // Store in CHW format
        let baseIndex = y * width + x
        floatPtr[baseIndex] = r                           // R channel
        floatPtr[width * height + baseIndex] = g          // G channel
        floatPtr[2 * width * height + baseIndex] = b      // B channel
      }
    }
    
    return tensorData
  }
  
  /// Postprocesses ONNX model outputs to detection results
  private func postprocessOutputs(_ outputs: [String: ORTValue]) throws -> [DetectionResult] {
    // This is a simplified implementation for YOLO-style outputs
    // In practice, you'd need to adapt this based on your specific ONNX model's output format
    
    guard let output = outputs.values.first else {
      return []
    }
    
    let tensorData = try output.tensorData()
    let floatPtr = tensorData.bytes.assumingMemoryBound(to: Float.self)
    
    var detections: [DetectionResult] = []
    let confidenceThreshold = getConfidenceThreshold()
    
    // Parse YOLO-style output (simplified)
    // This would need to be adapted based on your specific model's output format
    let numDetections = tensorData.length / (MemoryLayout<Float>.size * 6) // x, y, w, h, conf, class
    
    for i in 0..<numDetections {
      let baseIndex = i * 6
      let x = floatPtr[baseIndex]
      let y = floatPtr[baseIndex + 1]
      let width = floatPtr[baseIndex + 2]
      let height = floatPtr[baseIndex + 3]
      let confidence = floatPtr[baseIndex + 4]
      let classId = Int(floatPtr[baseIndex + 5])
      
      if confidence >= confidenceThreshold {
        let boundingBox = CGRect(x: CGFloat(x - width/2), 
                               y: CGFloat(y - height/2), 
                               width: CGFloat(width), 
                               height: CGFloat(height))
        
        let detection = DetectionResult(
          boundingBox: boundingBox,
          confidence: confidence,
          label: getClassLabel(for: classId)
        )
        
        detections.append(detection)
      }
    }
    
    return detections
  }
  
  /// Gets class label for the given class ID
  private func getClassLabel(for classId: Int) -> String {
    // COCO dataset class names (simplified)
    let cocoClasses = [
      "person", "bicycle", "car", "motorcycle", "airplane", "bus", "train", "truck",
      "boat", "traffic light", "fire hydrant", "stop sign", "parking meter", "bench",
      "bird", "cat", "dog", "horse", "sheep", "cow", "elephant", "bear", "zebra",
      "giraffe", "backpack", "umbrella", "handbag", "tie", "suitcase", "frisbee"
      // ... add more classes as needed
    ]
    
    return classId < cocoClasses.count ? cocoClasses[classId] : "Unknown"
  }
}
