import AVFoundation
import Swift<PERSON><PERSON>ore
import UIKit
import Vision

/// Handles object detection using Vision and CoreML
class DetectionService: ObservableObject {

  // MARK: - Settings Manager
  private let appSettings = AppSettings.shared

  // MARK: - Published Properties
  @Published var currentDetections: [DetectionResult] = []
  @Published var error: DetectionError?

  // MARK: - Private Properties
  private var visionModel: VNCoreMLModel?
  private var frameCounter = 0
  private let processingQueue = DispatchQueue(
    label: "detection.processing.queue", qos: .userInitiated)
  private var lastProcessingTime = Date()

  // MARK: - Initialization
  static let shared = DetectionService()
  private init() {}

  // MARK: - Public Methods

  /// Loads a CoreML model for detection
  func loadModel(_ modelInfo: MLModelInfo) async {
    await MainActor.run { error = nil }

    do {
      guard let modelURL = modelInfo.modelURL else {
        throw DetectionError.modelNotFound
      }
      let config = MLModelConfiguration()
      config.computeUnits = .all

      guard modelURL.pathExtension == Constants.compiledModelExtension,
        let mlModel = try? MLModel(contentsOf: modelURL, configuration: config)
      else {
        throw DetectionError.invalidModelFormat
      }

      let visionModel = try VNCoreMLModel(for: mlModel)

      await MainActor.run { self.visionModel = visionModel }

      print("Successfully loaded model: \(modelInfo.name)")
    } catch {
      await MainActor.run { self.error = .modelLoadingFailed(error.localizedDescription) }
      print("Failed to load model: \(error)")
    }
  }

  /// Processes a video frame for object detection
  func processFrame(_ ciImage: CIImage) {
    guard appSettings.isDetectionEnabled,
      let visionModel = visionModel,
      shouldProcessFrame()
    else {
      return
    }

    processingQueue.async { [weak self] in
      self?.performDetection(on: ciImage, using: visionModel)
    }
  }

  /// Clears current detections
  func clearDetections() {
    currentDetections = []
  }

  // MARK: - Private Methods

  private func shouldProcessFrame() -> Bool {
    frameCounter += 1

    // Process every nth frame based on settings
    guard frameCounter % appSettings.frameProcessingInterval == 0 else {
      return false
    }

    // Throttle processing to avoid overwhelming the system
    let now = Date()
    let timeSinceLastProcessing = now.timeIntervalSince(lastProcessingTime)
    guard timeSinceLastProcessing >= 0.1 else {  // Minimum 100ms between processing
      return false
    }

    lastProcessingTime = now
    return true
  }

  private func performDetection(on ciImage: CIImage, using model: VNCoreMLModel) {
    let request = VNCoreMLRequest(model: model) { [weak self] request, error in
      self?.handleDetectionResults(request: request, error: error)
    }

    // Configure request
    request.imageCropAndScaleOption = .scaleFill

    let handler = VNImageRequestHandler(ciImage: ciImage)

    do {
      try handler.perform([request])
    } catch {
      Task { @MainActor in self.error = .processingFailed(error.localizedDescription) }
    }
  }

  private func handleDetectionResults(request: VNRequest, error: Error?) {
    if let error = error {
      Task { @MainActor in self.error = .processingFailed(error.localizedDescription) }
      return
    }

    guard let observations = request.results as? [VNRecognizedObjectObservation] else {
      return
    }

    // Filter observations by confidence threshold
    let detections = observations.filter {
      $0.confidence >= Float(appSettings.confidenceThreshold)
    }
    .map { DetectionResult(from: $0) }

    Task { @MainActor in self.currentDetections = detections }
  }
}

// MARK: - Supporting Types

/// Detection-related errors
enum DetectionError: LocalizedError {
  case modelNotFound
  case modelLoadingFailed(String)
  case processingFailed(String)
  case invalidModelFormat

  var errorDescription: String? {
    switch self {
    case .modelNotFound:
      return "Model file not found."
    case .modelLoadingFailed(let message):
      return "Failed to load model: \(message)"
    case .processingFailed(let message):
      return "Detection processing failed: \(message)"
    case .invalidModelFormat:
      return Constants.ErrorMessages.invalidModelFormat
    }
  }
}
