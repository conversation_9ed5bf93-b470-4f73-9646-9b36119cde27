import AVFoundation
import Combine
import CoreImage
import SwiftUI<PERSON>ore

/// ViewModel for camera and detection functionality
class CameraViewModel: ObservableObject {

  // MARK: - Settings Manager
  var appSettings = AppSettings.shared

  // MARK: - Published Properties
  @Published var previewImage: Image?
  @Published var cameraPermissionStatus: AVAuthorizationStatus = .notDetermined
  @Published var currentDetections: [DetectionResult] = []
  @Published var error: String?

  // MARK: - Services
  private let cameraService = CameraService.shared
  private let detectionManager = DetectionManager.shared
  private let modelManager = ModelManagerService.shared

  // MARK: - Private Properties
  private var cancellables = Set<AnyCancellable>()

  // MARK: - Initialization
  init() {
    setupBindings()
    setupSettingsObservation()
    loadInitialModel()
  }

  // MARK: - Public Methods

  /// Starts the camera session
  func startCamera() {
    cameraService.startSession()
  }

  /// Stops the camera session
  func stopCamera() {
    cameraService.stopSession()
  }

  /// Requests camera permission
  func requestCameraPermission() {
    cameraService.requestCameraPermission()
  }

  /// Toggles confidence score display
  func toggleConfidenceScores() {
    appSettings.showConfidenceScores.toggle()
  }

  /// Loads a new model for detection
  func loadModel(_ model: UniversalModelInfo) {
    Task {
      await detectionManager.loadModel(model)
      await MainActor.run {
        self.modelManager.setSelectedModel(model)
      }
    }
  }

  /// Returns the camera capture session for preview
  func getCaptureSession() -> AVCaptureSession {
    return cameraService.getCaptureSession()
  }

  /// Clears current detections
  func clearDetections() {
    detectionManager.clearDetections()
  }

  // MARK: - Settings Access

  /// Access to detection enabled setting
  var isDetectionEnabled: Bool {
    get { appSettings.isDetectionEnabled }
    set { appSettings.isDetectionEnabled = newValue }
  }

  /// Access to confidence threshold setting
  var confidenceThreshold: Double {
    get { appSettings.confidenceThreshold }
    set { appSettings.confidenceThreshold = newValue }
  }

  /// Access to show confidence scores setting
  var showConfidenceScores: Bool {
    get { appSettings.showConfidenceScores }
    set { appSettings.showConfidenceScores = newValue }
  }

  // MARK: - Private Methods

  private func setupBindings() {
    // Bind camera service properties
    cameraService.$cameraPermissionStatus
      .receive(on: DispatchQueue.main)
      .assign(to: \.cameraPermissionStatus, on: self)
      .store(in: &cancellables)

    cameraService.$error
      .receive(on: DispatchQueue.main)
      .map { $0?.localizedDescription }
      .assign(to: \.error, on: self)
      .store(in: &cancellables)

    // Bind detection manager properties
    detectionManager.$currentDetections
      .receive(on: DispatchQueue.main)
      .assign(to: \.currentDetections, on: self)
      .store(in: &cancellables)

    detectionManager.$error
      .receive(on: DispatchQueue.main)
      .map { $0?.localizedDescription }
      .assign(to: \.error, on: self)
      .store(in: &cancellables)

    // Set camera service as frame delegate
    cameraService.frameDelegate = self
  }

  private func setupSettingsObservation() {
    // Listen to app settings changes to trigger UI updates and clear detections when needed
    appSettings.objectWillChange
      .receive(on: DispatchQueue.main)
      .sink { [weak self] _ in
        guard let self = self else { return }

        // Clear detections when detection is disabled or confidence threshold changes
        if !self.appSettings.isDetectionEnabled {
          self.detectionManager.clearDetections()
        }

        self.objectWillChange.send()
      }
      .store(in: &cancellables)
  }

  private func loadInitialModel() {
    Task {
      // Wait for model manager to load available models
      await modelManager.loadAvailableModels()

      await MainActor.run {
        // Try to load the previously selected model
        if let selectedModel = self.appSettings.selectedModel {
          self.loadModel(selectedModel)
        }
      }
    }
  }
}

// MARK: - CameraFrameDelegate
extension CameraViewModel: CameraFrameDelegate {

  static private let ciContext = CIContext()

  func didReceiveImage(_ ciImage: CIImage) {
    guard let cgImage = CameraViewModel.ciContext.createCGImage(ciImage, from: ciImage.extent)
    else { return }
    Task { @MainActor in self.previewImage = Image(decorative: cgImage, scale: 1) }

    detectionManager.processFrame(ciImage)
  }
}
