import Combine
import Foundation

/// ViewModel for app settings and preferences
class SettingsViewModel: ObservableObject {

  // MARK: - Settings Manager
  var appSettings = AppSettings.shared

  // MARK: - Services
  private let modelManager = ModelManagerService.shared

  // MARK: - Private Properties
  private var cancellables = Set<AnyCancellable>()
  private let fileManager = FileManager.default

  // MARK: - Initialization
  init() {
    setupBindings()
  }

  // MARK: - Private Methods

  private func setupBindings() {
    // Listen to app settings changes to trigger UI updates
    appSettings.objectWillChange
      .receive(on: DispatchQueue.main)
      .sink { [weak self] _ in
        self?.objectWillChange.send()
      }
      .store(in: &cancellables)
  }

  // MARK: - Public Methods

  /// Resets settings to default values
  func resetToDefaults() {
    appSettings.resetToDefaults()
  }

  /// Clears all downloaded models
  func clearAllDownloadedModels() {
    Task { await modelManager.clearAllDownloadedModels() }
  }
}
