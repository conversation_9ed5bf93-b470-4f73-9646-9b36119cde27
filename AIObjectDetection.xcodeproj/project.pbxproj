// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		361F96C22DF2EA2600F00AF8 /* .gitignore in Resources */ = {isa = PBXBuildFile; fileRef = 361F96C12DF2EA2400F00AF8 /* .gitignore */; };
		3645F96D2DFBBD5100669C63 /* onnxruntime in Frameworks */ = {isa = PBXBuildFile; productRef = 3645F96C2DFBBD5100669C63 /* onnxruntime */; };
		3645F96F2DFBBD5100669C63 /* onnxruntime_extensions in Frameworks */ = {isa = PBXBuildFile; productRef = 3645F96E2DFBBD5100669C63 /* onnxruntime_extensions */; };
		36C3310B2DEEE87500ED6A12 /* SimpleTokenizer in Frameworks */ = {isa = PBXBuildFile; productRef = 36C3310A2DEEE87500ED6A12 /* SimpleTokenizer */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		361F96C12DF2EA2400F00AF8 /* .gitignore */ = {isa = PBXFileReference; lastKnownFileType = text; path = .gitignore; sourceTree = "<group>"; };
		3656C3462DED5E110032DE13 /* AIObjectDetection.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = AIObjectDetection.app; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		36EF20C82DED83BA00684102 /* Exceptions for "AIObjectDetection" folder in "AIObjectDetection" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 3656C3452DED5E110032DE13 /* AIObjectDetection */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		3656C3482DED5E110032DE13 /* AIObjectDetection */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				36EF20C82DED83BA00684102 /* Exceptions for "AIObjectDetection" folder in "AIObjectDetection" target */,
			);
			path = AIObjectDetection;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		3656C3432DED5E110032DE13 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3645F96D2DFBBD5100669C63 /* onnxruntime in Frameworks */,
				36C3310B2DEEE87500ED6A12 /* SimpleTokenizer in Frameworks */,
				3645F96F2DFBBD5100669C63 /* onnxruntime_extensions in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		3656C33D2DED5E110032DE13 = {
			isa = PBXGroup;
			children = (
				361F96C12DF2EA2400F00AF8 /* .gitignore */,
				3656C3482DED5E110032DE13 /* AIObjectDetection */,
				3656C3472DED5E110032DE13 /* Products */,
			);
			sourceTree = "<group>";
		};
		3656C3472DED5E110032DE13 /* Products */ = {
			isa = PBXGroup;
			children = (
				3656C3462DED5E110032DE13 /* AIObjectDetection.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		3656C3452DED5E110032DE13 /* AIObjectDetection */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3656C3512DED5E130032DE13 /* Build configuration list for PBXNativeTarget "AIObjectDetection" */;
			buildPhases = (
				3656C3422DED5E110032DE13 /* Sources */,
				3656C3432DED5E110032DE13 /* Frameworks */,
				3656C3442DED5E110032DE13 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				3656C3482DED5E110032DE13 /* AIObjectDetection */,
			);
			name = AIObjectDetection;
			packageProductDependencies = (
				36C3310A2DEEE87500ED6A12 /* SimpleTokenizer */,
				3645F96C2DFBBD5100669C63 /* onnxruntime */,
				3645F96E2DFBBD5100669C63 /* onnxruntime_extensions */,
			);
			productName = AIObjectDetection;
			productReference = 3656C3462DED5E110032DE13 /* AIObjectDetection.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		3656C33E2DED5E110032DE13 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					3656C3452DED5E110032DE13 = {
						CreatedOnToolsVersion = 16.4;
					};
				};
			};
			buildConfigurationList = 3656C3412DED5E110032DE13 /* Build configuration list for PBXProject "AIObjectDetection" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 3656C33D2DED5E110032DE13;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				36C331092DEEE87500ED6A12 /* XCLocalSwiftPackageReference "../SimpleTokenizer" */,
				3645F96B2DFBBD5100669C63 /* XCRemoteSwiftPackageReference "onnxruntime-swift-package-manager" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 3656C3472DED5E110032DE13 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				3656C3452DED5E110032DE13 /* AIObjectDetection */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		3656C3442DED5E110032DE13 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				361F96C22DF2EA2600F00AF8 /* .gitignore in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		3656C3422DED5E110032DE13 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		3656C34F2DED5E130032DE13 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		3656C3502DED5E130032DE13 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_VERSION = 5.0;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		3656C3522DED5E130032DE13 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = T57AYQB255;
				ENABLE_PREVIEWS = YES;
				GCC_OPTIMIZATION_LEVEL = 3;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = AIObjectDetection/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "AI Detection";
				INFOPLIST_KEY_NSCameraUsageDescription = "This app needs camera access to perform real-time object detection.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.ssv.ai-detection";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		3656C3532DED5E130032DE13 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = T57AYQB255;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = AIObjectDetection/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "AI Detection";
				INFOPLIST_KEY_NSCameraUsageDescription = "This app needs camera access to perform real-time object detection.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.ssv.ai-detection";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		3656C3412DED5E110032DE13 /* Build configuration list for PBXProject "AIObjectDetection" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3656C34F2DED5E130032DE13 /* Debug */,
				3656C3502DED5E130032DE13 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3656C3512DED5E130032DE13 /* Build configuration list for PBXNativeTarget "AIObjectDetection" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3656C3522DED5E130032DE13 /* Debug */,
				3656C3532DED5E130032DE13 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		36C331092DEEE87500ED6A12 /* XCLocalSwiftPackageReference "../SimpleTokenizer" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = ../SimpleTokenizer;
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCRemoteSwiftPackageReference section */
		3645F96B2DFBBD5100669C63 /* XCRemoteSwiftPackageReference "onnxruntime-swift-package-manager" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/microsoft/onnxruntime-swift-package-manager";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.20.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		3645F96C2DFBBD5100669C63 /* onnxruntime */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3645F96B2DFBBD5100669C63 /* XCRemoteSwiftPackageReference "onnxruntime-swift-package-manager" */;
			productName = onnxruntime;
		};
		3645F96E2DFBBD5100669C63 /* onnxruntime_extensions */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3645F96B2DFBBD5100669C63 /* XCRemoteSwiftPackageReference "onnxruntime-swift-package-manager" */;
			productName = onnxruntime_extensions;
		};
		36C3310A2DEEE87500ED6A12 /* SimpleTokenizer */ = {
			isa = XCSwiftPackageProductDependency;
			productName = SimpleTokenizer;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 3656C33E2DED5E110032DE13 /* Project object */;
}
