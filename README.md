# AI Object Detection Demo - Refactored Architecture

This SwiftUI app demonstrates real-time object detection with support for both CoreML and ONNX models through a scalable abstraction layer.

## Architecture Overview

### Model Abstraction Layer

The app now uses a protocol-based architecture that allows easy integration of different model formats:

#### Core Protocols
- **`ObjectDetectionModel`**: Main protocol defining the interface for all model types
- **`ModelInfo`**: Protocol for model metadata abstraction

#### Model Handlers
- **`CoreMLModelHandler`**: Handles CoreML (.mlmodelc) models using Vision framework
- **`ONNXModelHandler`**: Handles ONNX (.onnx) models using ONNX Runtime
- **`BaseModelHandler`**: Base class providing common functionality

#### Factory Pattern
- **`ModelFactory`**: Creates appropriate model handlers based on model type

### Detection Pipeline

#### DetectionManager
The unified `DetectionManager` class:
- Accepts any `ObjectDetectionModel` instance
- Handles frame capture, throttling, and prediction
- Notifies SwiftUI views with detection results
- Manages model lifecycle and error handling

#### Key Features
- **Frame Throttling**: Configurable processing intervals to optimize performance
- **Confidence Filtering**: Adjustable confidence thresholds
- **Error Handling**: Comprehensive error reporting and recovery
- **Memory Management**: Proper model loading/unloading

### Model Management

#### UniversalModelInfo
Replaces the old `MLModelInfo` with support for multiple model types:
- **Model Type**: Enum distinguishing between CoreML and ONNX
- **File Extensions**: Automatic handling of .mlmodelc and .onnx files
- **Download URLs**: Support for different model sources

#### ModelManagerService
Enhanced to support multiple model formats:
- **Multi-format Downloads**: Handles both CoreML and ONNX model downloads
- **Compilation**: Automatic CoreML model compilation
- **Storage**: Organized file management for different model types

## Supported Model Types

### CoreML Models
- **Format**: `.mlmodelc` (compiled CoreML models)
- **Framework**: Vision + CoreML
- **Preprocessing**: Handled by Vision framework
- **Performance**: Optimized for Apple Silicon

### ONNX Models
- **Format**: `.onnx` (Open Neural Network Exchange)
- **Framework**: ONNX Runtime
- **Preprocessing**: Custom image preprocessing pipeline
- **Performance**: Cross-platform compatibility

## Usage

### Adding New Model Types

To add support for a new model format:

1. **Add Model Type**:
```swift
enum ModelType: String, CaseIterable, Codable {
  case coreML = "mlmodelc"
  case onnx = "onnx"
  case newFormat = "newext"  // Add your format
}
```

2. **Create Model Handler**:
```swift
class NewFormatModelHandler: BaseModelHandler {
  override func loadModel() async throws {
    // Implement model loading
  }
  
  override func predict(ciImage: CIImage) async throws -> [DetectionResult] {
    // Implement prediction
  }
}
```

3. **Update Factory**:
```swift
static func createModel(for modelInfo: any ModelInfo) -> ObjectDetectionModel {
  switch modelInfo.modelType {
  case .newFormat:
    return NewFormatModelHandler(modelInfo: modelInfo)
  // ... existing cases
  }
}
```

### Model Configuration

Models are configured in `ModelManagerService.loadModels()`:

```swift
UniversalModelInfo(
  name: "YOLOv5s",
  downloadURL: "https://example.com/yolov5s.onnx",
  fileSize: 14_100_000,
  modelType: .onnx
)
```

## Benefits of the New Architecture

### Scalability
- **Easy Extension**: Add new model formats without changing existing code
- **Modular Design**: Each model type is self-contained
- **Factory Pattern**: Centralized model creation logic

### Maintainability
- **Separation of Concerns**: UI logic separated from model-specific code
- **Protocol-Based**: Clear interfaces and contracts
- **Error Handling**: Comprehensive error types and handling

### Performance
- **Lazy Loading**: Models loaded only when needed
- **Memory Management**: Proper cleanup and resource management
- **Optimized Processing**: Format-specific optimizations

### Testing
- **Mockable Protocols**: Easy to create test doubles
- **Isolated Components**: Each part can be tested independently
- **Dependency Injection**: Services can be easily replaced for testing

## Dependencies

- **ONNX Runtime**: For ONNX model support
- **Vision Framework**: For CoreML model integration
- **CoreML**: For Apple's machine learning models
- **SwiftUI**: For the user interface
- **Combine**: For reactive programming

## File Structure

```
AIObjectDetection/
├── Models/
│   ├── ObjectDetectionModel.swift      # Protocol definitions
│   ├── MLModelInfo.swift               # Model metadata (now UniversalModelInfo)
│   └── DetectionResult.swift           # Detection result structure
├── Services/
│   ├── DetectionManager.swift          # Unified detection pipeline
│   ├── CoreMLModelHandler.swift        # CoreML implementation
│   ├── ONNXModelHandler.swift          # ONNX implementation
│   ├── ModelManagerService.swift       # Multi-format model management
│   ├── CameraService.swift             # Camera handling
│   └── AppSettings.swift               # App configuration
├── ViewModels/
│   ├── CameraViewModel.swift           # Camera and detection UI logic
│   ├── ModelManagerViewModel.swift     # Model management UI logic
│   └── SettingsViewModel.swift         # Settings UI logic
└── Views/
    ├── CameraView.swift                # Main camera interface
    ├── ModelListView.swift             # Model selection interface
    └── SettingsView.swift              # App settings interface
```

This architecture provides a solid foundation for expanding the app's capabilities while maintaining clean, testable, and maintainable code.
